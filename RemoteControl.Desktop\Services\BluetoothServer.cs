using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using InTheHand.Net;
using InTheHand.Net.Bluetooth;
using InTheHand.Net.Sockets;
using Newtonsoft.Json;
using RemoteControl.Desktop.Interfaces;
using RemoteControl.Desktop.Models;

namespace RemoteControl.Desktop.Services
{
    /// <summary>
    /// 蓝牙服务器 - 使用蓝牙串口通信(SPP)
    /// </summary>
    public class BluetoothServer : IConnectionServer, IDisposable
    {
        private BluetoothListener _bluetoothListener;
        private bool _isRunning = false;
        private readonly object _lock = new object();
        private readonly AppSettings _settings;
        private int _connectedClients = 0;
        private CancellationTokenSource _cancellationTokenSource;

        // 蓝牙串口服务UUID
        private static readonly Guid ServiceUuid = new Guid("00001101-0000-1000-8000-00805F9B34FB");

        public event Action<string> OnLog;
        public event Action OnClientConnected;
        public event Action OnClientDisconnected;

        public bool IsRunning
        {
            get
            {
                lock (_lock)
                {
                    return _isRunning;
                }
            }
        }

        public ConnectionMode ConnectionMode => ConnectionMode.Bluetooth;

        public int ConnectedClients
        {
            get
            {
                lock (_lock)
                {
                    return _connectedClients;
                }
            }
        }

        public BluetoothServer(AppSettings settings)
        {
            _settings = settings;
        }

        public void Start()
        {
            lock (_lock)
            {
                if (_isRunning) return;

                try
                {
                    // 检查蓝牙是否可用
                    try
                    {
                        // 简化的蓝牙检查 - 直接尝试创建监听器
                        OnLog?.Invoke("正在初始化蓝牙服务...");
                    }
                    catch (Exception ex)
                    {
                        OnLog?.Invoke($"蓝牙初始化警告: {ex.Message}");
                        // 继续尝试启动服务，某些情况下即使检查失败也可能工作
                    }

                    // 创建蓝牙监听器
                    _bluetoothListener = new BluetoothListener(ServiceUuid);
                    _bluetoothListener.Start();

                    _isRunning = true;
                    _cancellationTokenSource = new CancellationTokenSource();

                    OnLog?.Invoke($"蓝牙服务器已启动，服务名称: {_settings.BluetoothServiceName}");

                    // 启动接受连接的任务
                    Task.Run(AcceptConnections);
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"启动蓝牙服务器失败: {ex.Message}");
                    throw;
                }
            }
        }

        public void Stop()
        {
            lock (_lock)
            {
                if (!_isRunning) return;

                try
                {
                    _isRunning = false;
                    _cancellationTokenSource?.Cancel();
                    _bluetoothListener?.Stop();
                    _bluetoothListener = null;

                    OnLog?.Invoke("蓝牙服务器已停止");
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"停止蓝牙服务器时出错: {ex.Message}");
                }
            }
        }

        public string GetConnectionInfo()
        {
            if (!IsRunning)
                return "蓝牙服务器未启动";

            try
            {
                return $"蓝牙: {_settings.BluetoothServiceName}";
            }
            catch (Exception ex)
            {
                return $"蓝牙: 获取信息失败 - {ex.Message}";
            }
        }

        private async Task AcceptConnections()
        {
            while (IsRunning && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var client = await Task.Run(() => _bluetoothListener.AcceptBluetoothClient(), _cancellationTokenSource.Token);

                    if (client != null)
                    {
                        lock (_lock)
                        {
                            _connectedClients++;
                        }

                        OnLog?.Invoke($"蓝牙客户端已连接: {client.RemoteMachineName}");
                        OnClientConnected?.Invoke();

                        // 为每个客户端启动处理任务
                        _ = Task.Run(() => HandleClient(client));
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"接受蓝牙连接时出错: {ex.Message}");
                    await Task.Delay(1000); // 等待一秒后重试
                }
            }
        }

        private async Task HandleClient(BluetoothClient client)
        {
            try
            {
                using (client)
                using (var stream = client.GetStream())
                using (var reader = new StreamReader(stream, Encoding.UTF8))
                {
                    string line;
                    while ((line = await reader.ReadLineAsync()) != null && IsRunning)
                    {
                        ProcessBluetoothMessage(line);
                    }
                }
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"处理蓝牙客户端时出错: {ex.Message}");
            }
            finally
            {
                lock (_lock)
                {
                    _connectedClients = Math.Max(0, _connectedClients - 1);
                }

                OnLog?.Invoke("蓝牙客户端已断开");
                OnClientDisconnected?.Invoke();
            }
        }

        private Task ProcessBluetoothMessage(string message)
        {
            try
            {
                var data = JsonConvert.DeserializeObject<dynamic>(message);
                string type = data.type;

                switch (type)
                {
                    case "move":
                        double deltaX = (double)data.deltaX * _settings.MouseSensitivity;
                        double deltaY = (double)data.deltaY * _settings.MouseSensitivity;
                        InputSimulator.MoveMouse((int)deltaX, (int)deltaY);
                        break;

                    case "click":
                        string button = data.button;
                        if (button == "left")
                            InputSimulator.LeftClick();
                        else if (button == "right")
                            InputSimulator.RightClick();
                        break;

                    case "scroll":
                        double scrollX = (double)data.deltaX * _settings.ScrollSensitivity;
                        double scrollY = (double)data.deltaY * _settings.ScrollSensitivity;
                        InputSimulator.Scroll((int)scrollY);
                        break;

                    case "key":
                        string key = data.key;
                        InputSimulator.SendKey(key);
                        break;

                    case "shortcut":
                        string keys = data.keys;
                        InputSimulator.SendShortcut(keys);
                        break;

                    case "bulkText":
                        string text = data.text;
                        InputSimulator.SendText(text);
                        break;

                    case "dragStart":
                        InputSimulator.StartDrag();
                        break;

                    case "drag":
                        double dragDeltaX = (double)data.deltaX;
                        double dragDeltaY = (double)data.deltaY;
                        InputSimulator.DragMove((int)dragDeltaX, (int)dragDeltaY);
                        break;

                    case "dragEnd":
                        InputSimulator.EndDrag();
                        break;
                }
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"处理蓝牙消息失败: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        public void Dispose()
        {
            Stop();
            _cancellationTokenSource?.Dispose();
        }
    }
}
