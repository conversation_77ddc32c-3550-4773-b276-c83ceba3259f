using System;
using InTheHand.Net;
using InTheHand.Net.Bluetooth;
using InTheHand.Net.Sockets;

class BluetoothDiagnostic
{
    static void Main()
    {
        Console.WriteLine("=== 蓝牙诊断工具 ===\n");

        try
        {
            // 检查蓝牙支持
            Console.WriteLine("1. 检查蓝牙支持...");

            var radios = BluetoothRadio.AllRadios;
            Console.WriteLine($"   找到 {radios.Length} 个蓝牙适配器");

            if (radios.Length == 0)
            {
                Console.WriteLine("   ❌ 未找到蓝牙适配器");
                return;
            }

            // 显示蓝牙适配器信息
            for (int i = 0; i < radios.Length; i++)
            {
                var radio = radios[i];
                Console.WriteLine($"   适配器 {i + 1}:");
                Console.WriteLine($"     名称: {radio.Name}");
                Console.WriteLine($"     地址: {radio.LocalAddress}");
                Console.WriteLine($"     模式: {radio.Mode}");
                Console.WriteLine($"     状态: {(radio.Mode != RadioMode.PowerOff ? "开启" : "关闭")}");
            }

            // 尝试设置为可发现模式
            Console.WriteLine("\n2. 设置蓝牙为可发现模式...");
            var primaryRadio = radios[0];

            if (primaryRadio.Mode == RadioMode.PowerOff)
            {
                Console.WriteLine("   ❌ 蓝牙适配器已关闭，请先开启蓝牙");
                return;
            }

            try
            {
                primaryRadio.Mode = RadioMode.Discoverable;
                Console.WriteLine($"   ✅ 蓝牙模式已设置为: {primaryRadio.Mode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ 设置蓝牙模式失败: {ex.Message}");
            }

            // 尝试创建蓝牙服务
            Console.WriteLine("\n3. 创建蓝牙服务...");
            var serviceUuid = new Guid("00001101-0000-1000-8000-00805F9B34FB");

            try
            {
                var listener = new BluetoothListener(serviceUuid);
                listener.Start();

                Console.WriteLine("   ✅ 蓝牙服务创建成功");
                Console.WriteLine($"   服务UUID: {serviceUuid}");
                Console.WriteLine($"   本地地址: {primaryRadio.LocalAddress}");

                // 设置服务名称
                try
                {
                    listener.ServiceName = "RemoteControl";
                    Console.WriteLine("   ✅ 服务名称设置为: RemoteControl");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ 设置服务名称失败: {ex.Message}");
                }

                Console.WriteLine("\n4. 服务状态:");
                Console.WriteLine("   🔵 蓝牙服务正在运行...");
                Console.WriteLine("   📱 现在可以尝试从手机连接");
                Console.WriteLine($"   🔍 在手机蓝牙设置中查找: RemoteControl 或 {primaryRadio.LocalAddress}");

                Console.WriteLine("\n按任意键停止服务...");
                Console.ReadKey();

                listener.Stop();
                Console.WriteLine("   ✅ 蓝牙服务已停止");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建蓝牙服务失败: {ex.Message}");
                Console.WriteLine($"   详细错误: {ex}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 诊断过程出错: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }

        Console.WriteLine("\n诊断完成，按任意键退出...");
        Console.ReadKey();
    }
}
