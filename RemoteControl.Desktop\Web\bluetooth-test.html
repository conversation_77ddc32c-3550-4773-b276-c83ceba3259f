<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝牙连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #2C3E50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: #34495E;
            border-radius: 10px;
        }
        button {
            background: #3498DB;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            width: 200px;
        }
        button:hover {
            background: #2980B9;
        }
        button:disabled {
            background: #7F8C8D;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            background: #1ABC9C;
        }
        .error {
            background: #E74C3C;
        }
        .info {
            background: #F39C12;
        }
        .touchpad {
            width: 300px;
            height: 200px;
            background: #2C3E50;
            border: 2px solid #3498DB;
            border-radius: 10px;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            user-select: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔵 蓝牙连接测试</h1>
        
        <div id="status" class="status info">
            准备连接蓝牙设备
        </div>
        
        <button id="connectBtn" onclick="connectBluetooth()">
            📱 连接蓝牙设备
        </button>
        
        <button id="disconnectBtn" onclick="disconnectBluetooth()" disabled>
            ❌ 断开连接
        </button>
        
        <div class="touchpad" id="touchpad">
            触控板测试区域
        </div>
        
        <div style="margin-top: 20px; font-size: 12px; color: #BDC3C7;">
            <p>使用说明：</p>
            <p>1. 确保手机和电脑已通过蓝牙配对</p>
            <p>2. 点击"连接蓝牙设备"按钮</p>
            <p>3. 选择"RemoteControl"设备</p>
            <p>4. 连接成功后可在触控板区域测试</p>
        </div>
    </div>

    <script>
        let bluetoothDevice = null;
        let bluetoothCharacteristic = null;
        let isConnected = false;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        async function connectBluetooth() {
            if (!navigator.bluetooth) {
                updateStatus('此浏览器不支持Web Bluetooth API', 'error');
                return;
            }

            try {
                updateStatus('正在搜索蓝牙设备...', 'info');
                
                // 请求蓝牙设备
                bluetoothDevice = await navigator.bluetooth.requestDevice({
                    filters: [
                        { name: 'RemoteControl' },
                        { namePrefix: 'Remote' }
                    ],
                    optionalServices: ['00001101-0000-1000-8000-00805f9b34fb']
                });

                updateStatus('正在连接到设备...', 'info');
                
                // 连接到GATT服务器
                const server = await bluetoothDevice.gatt.connect();
                
                updateStatus('正在获取服务...', 'info');
                
                // 获取串口服务
                const service = await server.getPrimaryService('00001101-0000-1000-8000-00805f9b34fb');
                
                // 获取特征值
                bluetoothCharacteristic = await service.getCharacteristic('00001101-0000-1000-8000-00805f9b34fb');
                
                isConnected = true;
                updateStatus(`已连接到: ${bluetoothDevice.name}`, 'status');
                
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                
                // 监听断开事件
                bluetoothDevice.addEventListener('gattserverdisconnected', onDisconnected);
                
                // 启用触控板
                enableTouchpad();
                
            } catch (error) {
                console.error('蓝牙连接失败:', error);
                updateStatus(`连接失败: ${error.message}`, 'error');
            }
        }

        function disconnectBluetooth() {
            if (bluetoothDevice && bluetoothDevice.gatt.connected) {
                bluetoothDevice.gatt.disconnect();
            }
        }

        function onDisconnected() {
            isConnected = false;
            bluetoothCharacteristic = null;
            updateStatus('蓝牙连接已断开', 'info');
            
            document.getElementById('connectBtn').disabled = false;
            document.getElementById('disconnectBtn').disabled = true;
            
            disableTouchpad();
        }

        function enableTouchpad() {
            const touchpad = document.getElementById('touchpad');
            touchpad.style.background = '#1ABC9C';
            touchpad.textContent = '触控板已激活';
            
            let lastTouch = { x: 0, y: 0 };
            
            touchpad.addEventListener('touchstart', (e) => {
                e.preventDefault();
                const touch = e.touches[0];
                lastTouch.x = touch.clientX;
                lastTouch.y = touch.clientY;
            });
            
            touchpad.addEventListener('touchmove', (e) => {
                e.preventDefault();
                const touch = e.touches[0];
                const deltaX = touch.clientX - lastTouch.x;
                const deltaY = touch.clientY - lastTouch.y;
                
                sendBluetoothMessage({
                    type: 'move',
                    deltaX: deltaX,
                    deltaY: deltaY
                });
                
                lastTouch.x = touch.clientX;
                lastTouch.y = touch.clientY;
            });
            
            touchpad.addEventListener('click', () => {
                sendBluetoothMessage({
                    type: 'click',
                    button: 'left'
                });
            });
        }

        function disableTouchpad() {
            const touchpad = document.getElementById('touchpad');
            touchpad.style.background = '#2C3E50';
            touchpad.textContent = '触控板测试区域';
            
            // 移除事件监听器
            const newTouchpad = touchpad.cloneNode(true);
            touchpad.parentNode.replaceChild(newTouchpad, touchpad);
        }

        async function sendBluetoothMessage(data) {
            if (!isConnected || !bluetoothCharacteristic) {
                return;
            }
            
            try {
                const message = JSON.stringify(data) + '\n';
                const encoder = new TextEncoder();
                const dataArray = encoder.encode(message);
                await bluetoothCharacteristic.writeValue(dataArray);
            } catch (error) {
                console.error('发送蓝牙消息失败:', error);
                updateStatus('发送消息失败', 'error');
            }
        }
    </script>
</body>
</html>
