<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Remote Control - 极客触控板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            background: #0d1117;
            color: #f0f6fc;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            padding: 8px;
        }

        .header {
            text-align: center;
            padding: 12px 16px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            margin-bottom: 8px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00d8ff, transparent);
        }

        .header h2 {
            font-size: 14px;
            font-weight: 600;
            color: #f0f6fc;
            margin: 0;
            letter-spacing: 1px;
        }

        .status {
            font-size: 10px;
            margin-top: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'SF Mono', monospace;
            color: #8b949e;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 6px;
            background: #da3633;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: #238636;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }

            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }

            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .touchpad {
            flex: 1;
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            margin-bottom: 8px;
            position: relative;
            overflow: hidden;
        }

        .touchpad::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 216, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .touchpad-area {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6e7681;
            font-size: 12px;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1;
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-bottom: 8px;
        }

        .btn {
            height: 48px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            color: #f0f6fc;
            font-size: 12px;
            font-weight: 500;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 216, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:active::before {
            left: 100%;
        }

        .btn:active {
            background: #30363d;
            border-color: #00d8ff;
            transform: scale(0.98);
        }

        .btn.active {
            background: #1f6feb;
            border-color: #1f6feb;
            color: #ffffff;
        }

        .keyboard-toggle {
            height: 40px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            color: #f0f6fc;
            font-size: 11px;
            font-family: inherit;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .keyboard-toggle:active {
            background: #30363d;
            transform: scale(0.98);
        }

        .keyboard {
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 12px 12px 0 0;
            padding: 12px;
            display: none;
            max-height: 40vh;
            overflow-y: auto;
        }

        .keyboard.show {
            display: block;
        }

        .key-row {
            display: flex;
            gap: 4px;
            margin-bottom: 6px;
            justify-content: center;
        }

        .key {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 4px;
            color: #f0f6fc;
            padding: 8px;
            min-width: 28px;
            font-size: 12px;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.1s ease;
            text-align: center;
        }

        .key:active {
            background: #1f6feb;
            border-color: #1f6feb;
            transform: scale(0.95);
        }

        .key.space {
            flex: 1;
            min-width: 120px;
        }

        .key.wide {
            min-width: 48px;
        }

        .connected {
            color: #238636;
        }

        .disconnected {
            color: #da3633;
        }

        /* 触控反馈 */
        .touch-feedback {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(0, 216, 255, 0.3);
            pointer-events: none;
            transform: translate(-50%, -50%) scale(0);
            animation: touch-ripple 0.3s ease-out;
        }

        @keyframes touch-ripple {
            0% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 1;
            }

            100% {
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 4px;
        }

        ::-webkit-scrollbar-track {
            background: #161b22;
        }

        ::-webkit-scrollbar-thumb {
            background: #30363d;
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #484f58;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h2>REMOTE CONTROL</h2>
            <div class="status" id="status">
                <span class="status-dot" id="statusDot"></span>
                <span id="statusText">CONNECTING...</span>
            </div>
        </div>

        <script>
            let ws = null;
            let isConnected = false;
            let lastTouch = {
                x: 0,
                y: 0
            };
            let isScrollMode = false;
            let touchStartTime = 0;
            let touchCount = 0;
            let lastTouchTime = 0;
            let touchTimeout = null;

            // 连接WebSocket
            function connect() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/`;

                ws = new WebSocket(wsUrl);

                ws.onopen = function () {
                    isConnected = true;
                    updateStatus('CONNECTED', 'connected');
                };

                ws.onclose = function () {
                    isConnected = false;
                    updateStatus('DISCONNECTED', 'disconnected');
                    setTimeout(connect, 2000);
                };

                ws.onerror = function () {
                    isConnected = false;
                    updateStatus('ERROR', 'disconnected');
                };
            }

            function updateStatus(text, className) {
                const statusText = document.getElementById('statusText');
                const statusDot = document.getElementById('statusDot');
                statusText.textContent = text;
                statusDot.className = `status-dot ${className}`;
            }

            function sendMessage(data) {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify(data));
                }
            }

            function createTouchFeedback(x, y) {
                const feedback = document.createElement('div');
                feedback.className = 'touch-feedback';
                feedback.style.left = x + 'px';
                feedback.style.top = y + 'px';
                document.body.appendChild(feedback);

                setTimeout(() => {
                    document.body.removeChild(feedback);
                }, 300);
            }

            // 触控板事件处理
            const touchpad = document.getElementById('touchpad');

            // 触摸开始
            touchpad.addEventListener('touchstart', function (e) {
                e.preventDefault();
                const touch = e.touches[0];
                const rect = touchpad.getBoundingClientRect();

                lastTouch.x = touch.clientX;
                lastTouch.y = touch.clientY;
                touchStartTime = Date.now();

                // 多点触控检测
                if (e.touches.length === 2) {
                    isScrollMode = true;
                    document.getElementById('scrollMode').classList.add('active');
                }

                // 双击检测
                const currentTime = Date.now();
                if (currentTime - lastTouchTime < 300) {
                    touchCount++;
                    if (touchCount === 2) {
                        // 双击 = 右键
                        sendMessage({
                            type: 'click',
                            button: 'right'
                        });
                        createTouchFeedback(touch.clientX - rect.left, touch.clientY - rect.top);
                        touchCount = 0;
                    }
                } else {
                    touchCount = 1;
                }
                lastTouchTime = currentTime;
            });

            // 触摸移动
            touchpad.addEventListener('touchmove', function (e) {
                e.preventDefault();

                if (e.touches.length === 1) {
                    const touch = e.touches[0];
                    const deltaX = touch.clientX - lastTouch.x;
                    const deltaY = touch.clientY - lastTouch.y;

                    if (isScrollMode) {
                        sendMessage({
                            type: 'scroll',
                            deltaX: deltaX * 2,
                            deltaY: deltaY * 2
                        });
                    } else {
                        sendMessage({
                            type: 'move',
                            deltaX: deltaX * 1.5,
                            deltaY: deltaY * 1.5
                        });
                    }

                    lastTouch.x = touch.clientX;
                    lastTouch.y = touch.clientY;
                } else if (e.touches.length === 2) {
                    // 两指滚动
                    const touch1 = e.touches[0];
                    const touch2 = e.touches[1];
                    const centerX = (touch1.clientX + touch2.clientX) / 2;
                    const centerY = (touch1.clientY + touch2.clientY) / 2;

                    const deltaX = centerX - lastTouch.x;
                    const deltaY = centerY - lastTouch.y;

                    sendMessage({
                        type: 'scroll',
                        deltaX: deltaX,
                        deltaY: deltaY * 3
                    });

                    lastTouch.x = centerX;
                    lastTouch.y = centerY;
                }
            });

            // 触摸结束
            touchpad.addEventListener('touchend', function (e) {
                e.preventDefault();

                if (e.touches.length === 0) {
                    const touchDuration = Date.now() - touchStartTime;

                    // 单击检测（短时间触摸且没有移动）
                    if (touchDuration < 200 && touchCount === 1) {
                        clearTimeout(touchTimeout);
                        touchTimeout = setTimeout(() => {
                            if (touchCount === 1) {
                                sendMessage({
                                    type: 'click',
                                    button: 'left'
                                });
                                const rect = touchpad.getBoundingClientRect();
                                createTouchFeedback(lastTouch.x - rect.left, lastTouch.y - rect.top);
                            }
                            touchCount = 0;
                        }, 250);
                    }

                    isScrollMode = false;
                    document.getElementById('scrollMode').classList.remove('active');
                }
            });

            // 鼠标事件（桌面端测试）
            let isMouseDown = false;

            touchpad.addEventListener('mousedown', function (e) {
                e.preventDefault();
                isMouseDown = true;
                lastTouch.x = e.clientX;
                lastTouch.y = e.clientY;
            });

            touchpad.addEventListener('mousemove', function (e) {
                if (isMouseDown) {
                    e.preventDefault();
                    const deltaX = e.clientX - lastTouch.x;
                    const deltaY = e.clientY - lastTouch.y;

                    if (isScrollMode) {
                        sendMessage({
                            type: 'scroll',
                            deltaX: deltaX,
                            deltaY: deltaY
                        });
                    } else {
                        sendMessage({
                            type: 'move',
                            deltaX: deltaX,
                            deltaY: deltaY
                        });
                    }

                    lastTouch.x = e.clientX;
                    lastTouch.y = e.clientY;
                }
            });

            touchpad.addEventListener('mouseup', function (e) {
                isMouseDown = false;
            });

            // 按钮事件
            document.getElementById('leftClick').addEventListener('click', function () {
                sendMessage({
                    type: 'click',
                    button: 'left'
                });
            });

            document.getElementById('rightClick').addEventListener('click', function () {
                sendMessage({
                    type: 'click',
                    button: 'right'
                });
            });

            document.getElementById('scrollMode').addEventListener('click', function () {
                isScrollMode = !isScrollMode;
                this.classList.toggle('active', isScrollMode);
            });

            // 键盘切换
            document.getElementById('keyboardToggle').addEventListener('click', function () {
                const keyboard = document.getElementById('keyboard');
                const isVisible = keyboard.classList.contains('show');

                if (isVisible) {
                    keyboard.classList.remove('show');
                    this.textContent = 'SHOW KEYBOARD';
                } else {
                    keyboard.classList.add('show');
                    this.textContent = 'HIDE KEYBOARD';
                }
            });

            // 键盘按键事件
            document.querySelectorAll('.key').forEach(key => {
                key.addEventListener('click', function () {
                    const keyValue = this.getAttribute('data-key');
                    sendMessage({
                        type: 'key',
                        key: keyValue
                    });
                });
            });

            // 防止页面滚动
            document.addEventListener('touchmove', function (e) {
                e.preventDefault();
            }, {
                passive: false
            });

            // 启动连接
            connect();
        </script>

        <div class="touchpad" id="touchpad">
            <div class="touchpad-area">
                TOUCHPAD AREA
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="leftClick">L-CLICK</button>
            <button class="btn" id="rightClick">R-CLICK</button>
            <button class="btn" id="scrollMode">SCROLL</button>
        </div>

        <button class="keyboard-toggle" id="keyboardToggle">SHOW KEYBOARD</button>

        <div class="keyboard" id="keyboard">
            <div class="key-row">
                <button class="key" data-key="1">1</button>
                <button class="key" data-key="2">2</button>
                <button class="key" data-key="3">3</button>
                <button class="key" data-key="4">4</button>
                <button class="key" data-key="5">5</button>
                <button class="key" data-key="6">6</button>
                <button class="key" data-key="7">7</button>
                <button class="key" data-key="8">8</button>
                <button class="key" data-key="9">9</button>
                <button class="key" data-key="0">0</button>
            </div>
            <div class="key-row">
                <button class="key" data-key="q">Q</button>
                <button class="key" data-key="w">W</button>
                <button class="key" data-key="e">E</button>
                <button class="key" data-key="r">R</button>
                <button class="key" data-key="t">T</button>
                <button class="key" data-key="y">Y</button>
                <button class="key" data-key="u">U</button>
                <button class="key" data-key="i">I</button>
                <button class="key" data-key="o">O</button>
                <button class="key" data-key="p">P</button>
            </div>
            <div class="key-row">
                <button class="key" data-key="a">A</button>
                <button class="key" data-key="s">S</button>
                <button class="key" data-key="d">D</button>
                <button class="key" data-key="f">F</button>
                <button class="key" data-key="g">G</button>
                <button class="key" data-key="h">H</button>
                <button class="key" data-key="j">J</button>
                <button class="key" data-key="k">K</button>
                <button class="key" data-key="l">L</button>
            </div>
            <div class="key-row">
                <button class="key" data-key="z">Z</button>
                <button class="key" data-key="x">X</button>
                <button class="key" data-key="c">C</button>
                <button class="key" data-key="v">V</button>
                <button class="key" data-key="b">B</button>
                <button class="key" data-key="n">N</button>
                <button class="key" data-key="m">M</button>
                <button class="key wide" data-key="Backspace">⌫</button>
            </div>
            <div class="key-row">
                <button class="key space" data-key=" ">SPACE</button>
                <button class="key wide" data-key="Enter">↵</button>
            </div>
        </div>
    </div>